using System;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.AccountServer.TokenExchangeClient;
using DocuSign.OneConfig.Extensions;

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using Moq;

using TaskManagementService.Core.Config;
using TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;

using Xunit;

namespace TaskManagementService.Tests.Services;

[Trait("TestType", "UnitTest")]
public sealed class AccountServerTokenExchangeClientTest : IDisposable
{
    private readonly Mock<IAccountServerConfig> _mockAccountServerConfig;
    private readonly Mock<ILogger<AccountServerTokenExchangeClient>> _mockLogger;
    private readonly Mock<IOptions<TokenExchangeClientSecretsManager>> _mockRsaSecretsManager;
    private readonly Mock<ILogger<HttpClientRetryHandler>> _mockHttpRetryHandlerLogger;
    private readonly Mock<ITokenExchangeClient> _mockTokenExchangeClient;
    private readonly Mock<IWatchedSingletonConfig<IAccountServerConfig>> _mockWatchedConfig;

    public AccountServerTokenExchangeClientTest()
    {
        _mockAccountServerConfig = new Mock<IAccountServerConfig>();
        _mockLogger = new Mock<ILogger<AccountServerTokenExchangeClient>>();
        _mockRsaSecretsManager = new Mock<IOptions<TokenExchangeClientSecretsManager>>();
        _mockHttpRetryHandlerLogger = new Mock<ILogger<HttpClientRetryHandler>>();
        _mockTokenExchangeClient = new Mock<ITokenExchangeClient>();
        _mockWatchedConfig = new Mock<IWatchedSingletonConfig<IAccountServerConfig>>();

        // Default setup for account server config
        _mockAccountServerConfig.Setup(x => x.AuthTokenUrl).Returns("https://test-auth.com/oauth/token");
        _mockAccountServerConfig.Setup(x => x.HttpClientRetryHandlerMaxTries).Returns(3);
        _mockAccountServerConfig.Setup(x => x.HttpClientRetryHandlerSleepIntervalMs).Returns(1000);
        _mockAccountServerConfig.Setup(x => x.ClientId).Returns("test-client-id");
        _mockAccountServerConfig.Setup(x => x.JwtScope).Returns("test-scope");
        _mockAccountServerConfig.Setup(x => x.IsLive).Returns(true);
        _mockAccountServerConfig.Setup(x => x.RetryCount).Returns(3);
        _mockAccountServerConfig.Setup(x => x.RetryWaitPeriodMs).Returns(200);

        // Setup watched config
        _mockWatchedConfig.Setup(x => x.Value).Returns(_mockAccountServerConfig.Object);

        // Setup RSA secrets manager
        var secretsManager = new TokenExchangeClientSecretsManager();
        _mockRsaSecretsManager.Setup(x => x.Value).Returns(secretsManager);
    }

    [Fact]
    public void ConstructorWithValidParametersCreatesInstance()
    {
        // Act
        var client = new AccountServerTokenExchangeClient(
            _mockAccountServerConfig.Object,
            _mockLogger.Object,
            _mockRsaSecretsManager.Object,
            _mockHttpRetryHandlerLogger.Object);

        // Assert
        Assert.NotNull(client);
    }

    [Fact]
    public void ConstructorWithNullAccountServerConfigThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() =>
            new AccountServerTokenExchangeClient(
                null!,
                _mockLogger.Object,
                _mockRsaSecretsManager.Object,
                _mockHttpRetryHandlerLogger.Object));
    }

    [Fact]
    public void ConstructorWithNullLoggerThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() =>
            new AccountServerTokenExchangeClient(
                _mockAccountServerConfig.Object,
                null!,
                _mockRsaSecretsManager.Object,
                _mockHttpRetryHandlerLogger.Object));
    }

    [Fact]
    public void ConstructorWithNullRsaSecretsManagerThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() =>
            new AccountServerTokenExchangeClient(
                _mockAccountServerConfig.Object,
                _mockLogger.Object,
                null!,
                _mockHttpRetryHandlerLogger.Object));
    }

    [Fact]
    public void ConstructorWithNullHttpRetryHandlerLoggerThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() =>
            new AccountServerTokenExchangeClient(
                _mockAccountServerConfig.Object,
                _mockLogger.Object,
                _mockRsaSecretsManager.Object,
                null!));
    }

    [Fact]
    public void InternalConstructorWithValidParametersCreatesInstance()
    {
        // Act
        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Assert
        Assert.NotNull(client);
    }

    [Fact]
    public async Task GetAppTokenAsyncWithSuccessfulTokenRetrievalReturnsToken()
    {
        // Arrange
        const string expectedToken = "test-token-12345";
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedToken, result);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Once);
    }

    [Fact]
    public async Task GetAppTokenAsync_WithNullTokenFromClient_ReturnsNull()
    {
        // Arrange
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync((string?)null);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.AtLeastOnce);
    }

    [Fact]
    public async Task GetAppTokenAsyncWithTokenClientExceptionThrowsException()
    {
        // Arrange
        var expectedException = new InvalidOperationException("Token service unavailable");
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ThrowsAsync(expectedException);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            client.GetAppTokenAsync(CancellationToken.None));

        Assert.Equal("Token service unavailable", exception.Message);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.AtLeastOnce);
    }

    [Fact]
    public async Task GetAppTokenAsync_LogsInformationMessage()
    {
        // Arrange
        const string expectedToken = "test-token-12345";
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Calling account server token client to retrieve application token")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task GetAppTokenAsync_WithRetryOnNullToken_RetriesAndSucceeds()
    {
        // Arrange
        const string expectedToken = "test-token-after-retry";
        var callCount = 0;

        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .Returns(() =>
            {
                callCount++;
                return callCount == 1 ? Task.FromResult<string?>(null) : Task.FromResult<string?>(expectedToken);
            });

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedToken, result);
        Assert.Equal(2, callCount);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Exactly(2));
    }

    [Fact]
    public async Task GetAppTokenAsync_WithMaxRetriesExceeded_ReturnsNull()
    {
        // Arrange
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync((string?)null);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
        // Should retry 3 times (RetryCount) + 1 initial attempt = 4 total calls
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Exactly(4));
    }

    [Fact]
    public async Task GetAppTokenAsync_WithCancellationToken_PassesCancellationToken()
    {
        // Arrange
        const string expectedToken = "test-token-12345";
        var cancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = cancellationTokenSource.Token;

        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(cancellationToken);

        // Assert
        Assert.Equal(expectedToken, result);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Once);
    }

    [Fact]
    public async Task GetAppTokenAsync_WithCancelledToken_ThrowsOperationCancelledException()
    {
        // Arrange
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel();

        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync("test-token");

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            client.GetAppTokenAsync(cancellationTokenSource.Token));
    }

    [Fact]
    public void Constructor_WithDifferentRetryConfiguration_CreatesRetryPolicyCorrectly()
    {
        // Arrange
        _mockAccountServerConfig.Setup(x => x.RetryCount).Returns(5);
        _mockAccountServerConfig.Setup(x => x.RetryWaitPeriodMs).Returns(500);

        // Act
        var client = new AccountServerTokenExchangeClient(
            _mockAccountServerConfig.Object,
            _mockLogger.Object,
            _mockRsaSecretsManager.Object,
            _mockHttpRetryHandlerLogger.Object);

        // Assert
        Assert.NotNull(client);
        // The retry policy is created internally, so we can only verify the client was created successfully
        // The actual retry behavior is tested in other test methods
    }

    [Fact]
    public async Task GetAppTokenAsync_WithEmptyStringToken_ReturnsEmptyString()
    {
        // Arrange
        const string expectedToken = "";
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedToken, result);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Once);
    }

    [Fact]
    public async Task GetAppTokenAsync_WithWhitespaceToken_ReturnsWhitespaceToken()
    {
        // Arrange
        const string expectedToken = "   ";
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedToken, result);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Once);
    }

    [Fact]
    public async Task GetAppTokenAsync_WithLongToken_ReturnsLongToken()
    {
        // Arrange
        var expectedToken = new string('a', 2000); // Very long token
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedToken, result);
        Assert.Equal(2000, result!.Length);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Once);
    }

    [Fact]
    public async Task GetAppTokenAsync_WithSpecialCharactersInToken_ReturnsTokenWithSpecialCharacters()
    {
        // Arrange
        const string expectedToken = "token-with-special-chars!@#$%^&*()_+-=[]{}|;':\",./<>?";
        _mockTokenExchangeClient
            .Setup(x => x.GetApplicationTokenAsync())
            .ReturnsAsync(expectedToken);

        var client = new AccountServerTokenExchangeClient(
            _mockWatchedConfig.Object,
            _mockLogger.Object,
            _mockTokenExchangeClient.Object);

        // Act
        var result = await client.GetAppTokenAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedToken, result);
        _mockTokenExchangeClient.Verify(x => x.GetApplicationTokenAsync(), Times.Once);
    }

    [Fact]
    public void Constructor_WithMinimalRetryConfiguration_CreatesClient()
    {
        // Arrange
        _mockAccountServerConfig.Setup(x => x.RetryCount).Returns(0);
        _mockAccountServerConfig.Setup(x => x.RetryWaitPeriodMs).Returns(1);

        // Act
        var client = new AccountServerTokenExchangeClient(
            _mockAccountServerConfig.Object,
            _mockLogger.Object,
            _mockRsaSecretsManager.Object,
            _mockHttpRetryHandlerLogger.Object);

        // Assert
        Assert.NotNull(client);
    }

    [Fact]
    public void Constructor_WithMaxRetryConfiguration_CreatesClient()
    {
        // Arrange
        _mockAccountServerConfig.Setup(x => x.RetryCount).Returns(10);
        _mockAccountServerConfig.Setup(x => x.RetryWaitPeriodMs).Returns(5000);

        // Act
        var client = new AccountServerTokenExchangeClient(
            _mockAccountServerConfig.Object,
            _mockLogger.Object,
            _mockRsaSecretsManager.Object,
            _mockHttpRetryHandlerLogger.Object);

        // Assert
        Assert.NotNull(client);
    }

    public void Dispose()
    {
        // No resources to dispose in this test class
        GC.SuppressFinalize(this);
    }
}
